# GB28181 Catalog查询实现说明

## 问题背景

在原始实现中，GB28181网关只有Catalog响应处理，没有主动发送Catalog查询的功能，导致：

1. 设备信息只能从内存中获取，没有从下级平台获取的途径
2. 下级安防平台无法将其管辖的视频源信息推送给上级
3. 获取设备接口直接返回模拟数据，缺乏真实的设备信息

## 解决方案

### 1. Catalog查询触发时机

根据GB28181协议标准，实现了三种Catalog查询触发机制：

#### 场景1：设备注册时触发
- 当下级平台发送REGISTER请求时
- 网关自动在注册成功后发送Catalog查询
- 确保新注册的平台设备信息能及时获取

#### 场景2：定时查询
- 每30分钟自动向所有在线平台发送Catalog查询
- 保持设备信息的实时性
- 只向最近10分钟内活跃的平台发送查询

#### 场景3：实时查询
- 当API请求获取设备列表时
- 如果内存中没有设备信息，主动发送Catalog查询
- 等待2秒获取响应，如无响应则返回模拟数据

### 2. 实现的关键组件

#### SIP服务器增强 (`internal/sip/server.go`)

```go
// 新增方法
func (s *Server) SendCatalog(platformID string) error
func (s *Server) ReceiveRegister(ctx context.Context, client server.Client) (*server.Response, error)
func (s *Server) startPeriodicCatalogQuery()
```

**SendCatalog方法**：
- 构造标准的GB28181 Catalog查询XML
- 通过SIP MESSAGE发送给指定平台
- 使用正确的Content-Type: `application/MANSCDP+xml`

**ReceiveRegister方法**：
- 处理平台注册请求
- 注册成功后异步发送Catalog查询
- 避免阻塞注册响应

**startPeriodicCatalogQuery方法**：
- 定时任务，每30分钟执行一次
- 遍历所有在线平台发送查询
- 使用goroutine避免阻塞

#### 核心业务逻辑增强 (`internal/core/logic.go`)

```go
// 接口扩展
type SIPServer interface {
    SendInvite(gbID, receiveIP string, receivePort int, ssrc string) error
    SendPTZControl(gbID, command string, speed int) error
    SendCatalog(platformID string) error  // 新增
}
```

**GetDevices方法增强**：
- 检查内存中是否有设备信息
- 无设备时主动发送Catalog查询
- 等待响应或使用模拟数据作为fallback

### 3. Catalog查询XML格式

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Query>
<CmdType>Catalog</CmdType>
<SN>1692123456</SN>
<DeviceID>34020000002000000001</DeviceID>
</Query>
```

### 4. 完整的交互流程

```mermaid
sequenceDiagram
    participant Platform as 下级平台
    participant Gateway as GB网关
    participant Client as 上级应用

    Note over Platform,Gateway: 场景1: 设备注册时触发
    Platform->>+Gateway: REGISTER (设备注册)
    Gateway-->>-Platform: 200 OK
    Gateway->>+Platform: MESSAGE (Catalog查询)
    Platform-->>-Gateway: 200 OK (Catalog响应)
    Note over Gateway: 更新内存中的设备列表

    Note over Platform,Gateway: 场景2: 定时查询
    loop 每隔30分钟
        Gateway->>+Platform: MESSAGE (Catalog查询)
        Platform-->>-Gateway: 200 OK (Catalog响应)
        Note over Gateway: 更新内存中的设备列表
    end

    Note over Gateway,Client: 场景3: 实时查询
    Client->>+Gateway: GET /api/v1/devices
    alt 内存中有设备且未过期
        Gateway-->>-Client: 返回缓存的设备列表
    else 内存中无设备或已过期
        Gateway->>+Platform: MESSAGE (Catalog查询)
        Platform-->>-Gateway: 200 OK (Catalog响应)
        Note over Gateway: 更新内存中的设备列表
        Gateway-->>Client: 返回最新的设备列表
    end
```

## 测试验证

### 运行测试脚本

```bash
# 启动服务器
make run

# 在另一个终端运行测试
./test_catalog.sh
```

### 预期行为

1. **首次调用设备列表API**：
   - 触发Catalog查询
   - 等待2秒获取响应
   - 如无真实平台连接，返回模拟数据

2. **平台注册时**：
   - 自动发送Catalog查询
   - 异步更新设备列表

3. **定时任务**：
   - 每30分钟自动查询
   - 保持设备信息最新

### 日志监控

```bash
# 查看详细日志
tail -f /var/log/gb-gateway.log

# 或查看控制台输出
```

关键日志信息：
- `Sending Catalog query` - 发送查询
- `Received Catalog` - 接收响应
- `Device updated` - 设备信息更新
- `Periodic Catalog query sent` - 定时查询

## 配置说明

当前实现使用硬编码的时间间隔，可以通过配置文件进行调整：

- 定时查询间隔：30分钟
- 平台在线判断：10分钟
- 实时查询等待：2秒

## 兼容性

- 完全兼容现有API接口
- 向后兼容，不影响现有功能
- 在没有真实平台连接时提供模拟数据
- 支持多平台并发查询

## 后续优化建议

1. **配置化时间参数**：将时间间隔移到配置文件
2. **智能查询策略**：根据平台活跃度调整查询频率
3. **缓存策略**：实现设备信息的TTL缓存
4. **错误重试**：添加查询失败的重试机制
5. **性能监控**：添加查询响应时间统计
