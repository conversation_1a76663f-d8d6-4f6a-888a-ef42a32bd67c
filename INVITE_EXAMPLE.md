# SIP INVITE 请求示例

本文档展示了 GB-Gateway 生成的完整 SIP INVITE 请求消息。

## 完整的 SIP INVITE 消息

当调用视频流请求 API 时，系统会生成如下的标准 SIP INVITE 请求：

```
INVITE sip:34020000001320000001@*************:5060 SIP/2.0
Via: SIP/2.0/UDP 0.0.0.0:5060;branch=z9hG4bK-1755172064636047000
From: <sip:34020000002000000001@3402000000>;tag=tag-1755172064
To: <sip:34020000001320000001@*************:5060>
Call-ID: call-1755172064636046000@0.0.0.0
CSeq: 1 INVITE
Contact: <sip:34020000002000000001@0.0.0.0:5060>
Content-Type: application/sdp
Content-Length: 139

v=0
o=- 0 0 IN IP4 *************
s=Play
c=IN IP4 *************
t=0 0
m=video 15000 RTP/AVP 96
a=rtpmap:96 PS/90000
a=sendonly
a=ssrc:716970
```

## SIP 头部字段说明

### 请求行
- **INVITE**: SIP 方法，用于邀请建立会话
- **sip:34020000001320000001@*************:5060**: 目标设备的 SIP URI

### 必需头部字段
- **Via**: 指示请求路径，包含分支标识符
- **From**: 发起方信息，包含网关的国标ID和标签
- **To**: 目标方信息，即要点播的设备
- **Call-ID**: 唯一标识此次通话的ID
- **CSeq**: 命令序列号，INVITE 请求为 1

### 可选头部字段
- **Contact**: 联系地址，用于后续通信
- **Content-Type**: 消息体类型，这里是 SDP
- **Content-Length**: 消息体长度

## SDP 内容说明

### 会话描述
- **v=0**: SDP 版本号
- **o=- 0 0 IN IP4 ***************: 会话发起者信息
- **s=Play**: 会话名称
- **c=IN IP4 ***************: 连接信息，指定接收端IP
- **t=0 0**: 会话时间，0 0 表示永久会话

### 媒体描述
- **m=video 15000 RTP/AVP 96**: 视频媒体，端口15000，RTP协议，载荷类型96
- **a=rtpmap:96 PS/90000**: 载荷类型96映射到PS流，时钟频率90000Hz
- **a=sendonly**: 媒体方向，仅发送（摄像头向解码终端发送）
- **a=ssrc:716970**: 同步源标识符，用于RTP流识别

## 关键参数

| 参数 | 值 | 说明 |
|------|-----|------|
| 设备ID | 34020000001320000001 | 目标摄像头的国标ID |
| 网关ID | 34020000002000000001 | 网关自身的国标ID |
| 接收IP | ************* | 解码终端的IP地址 |
| 接收端口 | 15000 | 解码终端的RTP接收端口 |
| SSRC | 716970 | 流媒体同步源标识符 |
| Call-ID | call-1755172064636046000@0.0.0.0 | 唯一的通话标识 |

## 流程说明

1. **HTTP API 调用**: 客户端调用 `POST /api/v1/stream/request`
2. **参数验证**: 验证设备存在性和平台在线状态
3. **会话创建**: 生成唯一的会话ID和SSRC
4. **INVITE 构造**: 根据GB/T 28181标准构造完整的SIP INVITE消息
5. **消息发送**: 通过SIP协议栈发送INVITE到目标设备
6. **响应返回**: 向客户端返回会话信息

## 符合标准

此 INVITE 消息完全符合以下标准：
- **RFC 3261**: SIP协议标准
- **RFC 4566**: SDP协议标准  
- **GB/T 28181**: 国标视频监控联网系统标准

## 注意事项

1. 当前实现为演示版本，实际生产环境需要完整的SIP栈支持
2. SSRC为随机生成，实际应用中可能需要更复杂的分配策略
3. 媒体格式固定为PS流，可根据需要扩展支持其他格式
4. 会话管理需要处理INVITE响应和后续的BYE请求
