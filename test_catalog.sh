#!/bin/bash

# Catalog查询功能测试脚本

set -e

BASE_URL="http://localhost:8080/api/v1"

echo "🚀 开始GB-Gateway Catalog查询功能测试..."
echo "=================================================="

# 检查服务器状态
echo "1. 检查服务器状态..."
if ! curl -s "$BASE_URL/../health" > /dev/null; then
    echo "❌ 服务器未运行，请先启动 gb-gateway"
    exit 1
fi
echo "✅ 服务器正在运行"

# 测试设备列表获取（这会触发Catalog查询）
echo ""
echo "2. 测试设备列表获取（触发Catalog查询）..."
RESPONSE=$(curl -s "$BASE_URL/devices")
echo "响应: $RESPONSE"

# 检查响应是否成功
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "✅ 设备列表获取成功"
    
    # 显示设备数量
    DEVICE_COUNT=$(echo "$RESPONSE" | jq -r '.data | length')
    echo "📊 设备数量: $DEVICE_COUNT"
    
    # 显示设备详情
    if [ "$DEVICE_COUNT" -gt 0 ]; then
        echo "📋 设备列表:"
        echo "$RESPONSE" | jq -r '.data[] | "  - \(.name) (\(.gb_id))"'
    fi
else
    echo "❌ 设备列表获取失败: $RESPONSE"
fi

# 等待一段时间再次查询，看是否有更新
echo ""
echo "3. 等待5秒后再次查询..."
sleep 5

RESPONSE2=$(curl -s "$BASE_URL/devices")
CODE2=$(echo "$RESPONSE2" | jq -r '.code')
if [ "$CODE2" = "0" ]; then
    DEVICE_COUNT2=$(echo "$RESPONSE2" | jq -r '.data | length')
    echo "📊 第二次查询设备数量: $DEVICE_COUNT2"
    
    if [ "$DEVICE_COUNT2" -gt "$DEVICE_COUNT" ]; then
        echo "✅ 设备数量增加，Catalog查询可能已生效"
    elif [ "$DEVICE_COUNT2" -eq "$DEVICE_COUNT" ]; then
        echo "ℹ️  设备数量未变化，可能使用了缓存数据"
    fi
else
    echo "❌ 第二次查询失败: $RESPONSE2"
fi

echo ""
echo "=================================================="
echo "🎯 测试完成！"
echo ""
echo "💡 说明："
echo "   - 如果没有真实的GB28181平台连接，系统会创建模拟设备数据"
echo "   - 在真实环境中，Catalog查询会向注册的平台发送SIP MESSAGE"
echo "   - 查看服务器日志可以看到详细的Catalog查询过程"
echo ""
echo "📝 查看日志命令："
echo "   tail -f /var/log/gb-gateway.log"
echo "   或者查看控制台输出"
