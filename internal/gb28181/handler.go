package gb28181

import (
	"context"
	"log/slog"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"

	"github.com/go-av/gosip/pkg/gb28181"
	"github.com/go-av/gosip/pkg/server"
)

// Handler GB28181协议处理器
type Handler struct {
	config       *config.Config
	stateManager state.Manager
}

// NewHandler 创建新的GB28181处理器
func NewHandler(cfg *config.Config, stateManager state.Manager) *Handler {
	return &Handler{
		config:       cfg,
		stateManager: stateManager,
	}
}

// Realm 返回SIP域
func (h *Handler) Realm() string {
	return h.config.Server.SIPDomain
}

// ServerSIPID 返回服务器SIP ID
func (h *Handler) ServerSIPID() string {
	return h.config.Server.SIPID
}

// Keepalive 处理心跳消息
func (h *Handler) Keepalive(ctx context.Context, client server.Client, msg *gb28181.Keepalive) (*server.Response, error) {
	slog.Info("Received Keepalive",
		"device_id", msg.DeviceID,
		"status", msg.Status,
		"client", client.User())

	// 更新设备状态
	err := h.stateManager.UpdateDeviceStatus(msg.DeviceID, msg.Status)
	if err != nil {
		slog.Error("Failed to update device status", "error", err, "device_id", msg.DeviceID)
	}

	return server.NewResponse(200, "OK"), nil
}

// DeviceInfo 处理设备信息查询响应
func (h *Handler) DeviceInfo(ctx context.Context, client server.Client, msg *gb28181.DeviceInfo) (*server.Response, error) {
	slog.Info("Received DeviceInfo",
		"device_id", msg.DeviceID,
		"device_name", msg.DeviceName,
		"manufacturer", msg.Manufacturer,
		"model", msg.Model)

	// 这里可以存储设备信息
	// TODO: 实现设备信息存储逻辑

	return server.NewResponse(200, "OK"), nil
}

// DeviceStatus 处理设备状态查询响应
func (h *Handler) DeviceStatus(ctx context.Context, client server.Client, msg *gb28181.DeviceStatus) (*server.Response, error) {
	slog.Info("Received DeviceStatus",
		"device_id", msg.DeviceID,
		"status", msg.Status)

	// 更新设备状态
	err := h.stateManager.UpdateDeviceStatus(msg.DeviceID, msg.Status)
	if err != nil {
		slog.Error("Failed to update device status", "error", err, "device_id", msg.DeviceID)
	}

	return server.NewResponse(200, "OK"), nil
}

// PresetQuery 处理预置位查询响应
func (h *Handler) PresetQuery(ctx context.Context, client server.Client, msg *gb28181.PresetQuery) (*server.Response, error) {
	slog.Info("Received PresetQuery",
		"device_id", msg.DeviceID)

	// TODO: 处理预置位查询响应
	return server.NewResponse(200, "OK"), nil
}

// Catalog 处理设备目录响应
func (h *Handler) Catalog(ctx context.Context, client server.Client, catalog *gb28181.Catalog) error {
	slog.Info("Received Catalog",
		"device_id", catalog.DeviceID,
		"sum_num", catalog.SumNum,
		"item_count", len(catalog.Item))

	// 更新设备目录
	for _, item := range catalog.Item {
		device := &models.Device{
			GBID:       item.DeviceID,
			Name:       item.Name,
			Status:     item.Status,
			IP:         item.IPAddres,
			PlatformID: catalog.DeviceID,
		}

		err := h.stateManager.UpdateDevice(device)
		if err != nil {
			slog.Error("Failed to update device", "error", err, "device_id", item.DeviceID)
		} else {
			slog.Debug("Device updated", "device_id", item.DeviceID, "name", item.Name)
		}
	}

	return nil
}

// Broadcast 处理广播消息
func (h *Handler) Broadcast(ctx context.Context, client server.Client, msg *gb28181.BroadcastResponse) {
	slog.Info("Received Broadcast",
		"device_id", msg.DeviceID)

	// TODO: 处理广播消息
}
