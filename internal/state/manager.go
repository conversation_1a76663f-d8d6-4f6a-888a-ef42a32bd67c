package state

import (
	"fmt"
	"log/slog"
	"sync"
	"time"

	"gb-gateway/pkg/models"
)

// Manager 状态管理器接口
type Manager interface {
	// Platform management
	RegisterPlatform(platform *models.Platform) error
	GetPlatform(id string) (*models.Platform, error)
	UpdatePlatformLastSeen(id string) error
	ListPlatforms() ([]*models.Platform, error)

	// Device management
	SetDevices(platformID string, devices []*models.Device) error
	GetDevices(platformID string) ([]*models.Device, error)
	GetDevice(gbID string) (*models.Device, error)
	UpdateDevice(device *models.Device) error
	UpdateDeviceStatus(gbID, status string) error

	// Session management
	CreateSession(session *models.StreamSession) error
	GetSession(sessionID string) (*models.StreamSession, error)
	DeleteSession(sessionID string) error
	ListSessions() ([]*models.StreamSession, error)
}

// InMemoryManager 基于内存的状态管理器
type InMemoryManager struct {
	platforms sync.Map // map[string]*models.Platform
	devices   sync.Map // map[string]*models.Device (key: gb_id)
	sessions  sync.Map // map[string]*models.StreamSession (key: session_id)
	mu        sync.RWMutex
}

// NewInMemoryManager 创建新的内存状态管理器
func NewInMemoryManager() *InMemoryManager {
	return &InMemoryManager{}
}

// RegisterPlatform 注册平台
func (m *InMemoryManager) RegisterPlatform(platform *models.Platform) error {
	if platform.ID == "" {
		return fmt.Errorf("platform ID cannot be empty")
	}

	platform.LastSeen = time.Now()
	m.platforms.Store(platform.ID, platform)

	slog.Info("Platform registered", "platform_id", platform.ID, "sip_uri", platform.SIPURI)
	return nil
}

// GetPlatform 获取平台信息
func (m *InMemoryManager) GetPlatform(id string) (*models.Platform, error) {
	if value, ok := m.platforms.Load(id); ok {
		return value.(*models.Platform), nil
	}
	return nil, fmt.Errorf("platform not found: %s", id)
}

// UpdatePlatformLastSeen 更新平台最后活跃时间
func (m *InMemoryManager) UpdatePlatformLastSeen(id string) error {
	if value, ok := m.platforms.Load(id); ok {
		platform := value.(*models.Platform)
		platform.LastSeen = time.Now()
		m.platforms.Store(id, platform)
		return nil
	}
	return fmt.Errorf("platform not found: %s", id)
}

// ListPlatforms 列出所有平台
func (m *InMemoryManager) ListPlatforms() ([]*models.Platform, error) {
	var platforms []*models.Platform
	m.platforms.Range(func(key, value any) bool {
		platforms = append(platforms, value.(*models.Platform))
		return true
	})
	return platforms, nil
}

// SetDevices 设置平台的设备列表
func (m *InMemoryManager) SetDevices(platformID string, devices []*models.Device) error {
	// 先清除该平台的旧设备
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		if device.PlatformID == platformID {
			m.devices.Delete(key)
		}
		return true
	})

	// 添加新设备
	for _, device := range devices {
		device.PlatformID = platformID
		m.devices.Store(device.GBID, device)
	}

	slog.Info("Devices updated", "platform_id", platformID, "device_count", len(devices))
	return nil
}

// GetDevices 获取平台的设备列表
func (m *InMemoryManager) GetDevices(platformID string) ([]*models.Device, error) {
	var devices []*models.Device
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		if device.PlatformID == platformID {
			devices = append(devices, device)
		}
		return true
	})
	return devices, nil
}

// GetDevice 根据国标ID获取设备
func (m *InMemoryManager) GetDevice(gbID string) (*models.Device, error) {
	if value, ok := m.devices.Load(gbID); ok {
		return value.(*models.Device), nil
	}
	return nil, fmt.Errorf("device not found: %s", gbID)
}

// UpdateDevice 更新设备信息
func (m *InMemoryManager) UpdateDevice(device *models.Device) error {
	if device == nil || device.GBID == "" {
		return fmt.Errorf("invalid device")
	}

	m.devices.Store(device.GBID, device)
	slog.Debug("Device updated", "gb_id", device.GBID, "name", device.Name)
	return nil
}

// UpdateDeviceStatus 更新设备状态
func (m *InMemoryManager) UpdateDeviceStatus(gbID, status string) error {
	if gbID == "" {
		return fmt.Errorf("invalid device ID")
	}

	value, exists := m.devices.Load(gbID)
	if !exists {
		return fmt.Errorf("device not found")
	}

	device := value.(*models.Device)
	device.Status = status
	m.devices.Store(gbID, device)

	slog.Debug("Device status updated", "gb_id", gbID, "status", status)
	return nil
}

// CreateSession 创建会话
func (m *InMemoryManager) CreateSession(session *models.StreamSession) error {
	if session.SessionID == "" {
		return fmt.Errorf("session ID cannot be empty")
	}

	session.StartTime = time.Now()
	m.sessions.Store(session.SessionID, session)

	slog.Info("Session created", "session_id", session.SessionID, "gb_id", session.GBID)
	return nil
}

// GetSession 获取会话
func (m *InMemoryManager) GetSession(sessionID string) (*models.StreamSession, error) {
	if value, ok := m.sessions.Load(sessionID); ok {
		return value.(*models.StreamSession), nil
	}
	return nil, fmt.Errorf("session not found: %s", sessionID)
}

// DeleteSession 删除会话
func (m *InMemoryManager) DeleteSession(sessionID string) error {
	if _, ok := m.sessions.Load(sessionID); ok {
		m.sessions.Delete(sessionID)
		slog.Info("Session deleted", "session_id", sessionID)
		return nil
	}
	return fmt.Errorf("session not found: %s", sessionID)
}

// ListSessions 列出所有会话
func (m *InMemoryManager) ListSessions() ([]*models.StreamSession, error) {
	var sessions []*models.StreamSession
	m.sessions.Range(func(key, value any) bool {
		sessions = append(sessions, value.(*models.StreamSession))
		return true
	})
	return sessions, nil
}
