package http

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"time"

	_ "gb-gateway/docs"
	"gb-gateway/internal/config"
	"gb-gateway/internal/core"
	"gb-gateway/pkg/models"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title GB28181 Gateway API
// @version 1.0
// @description GB28181 视频监控网关 RESTful API 文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1
// @schemes http https

// @tag.name devices
// @tag.description 设备管理相关接口

// @tag.name stream
// @tag.description 视频流控制相关接口

// @tag.name control
// @tag.description 设备控制相关接口

// @tag.name health
// @tag.description 健康检查相关接口

// Server HTTP服务器
type Server struct {
	config     *config.Config
	coreLogic  *core.Logic
	httpServer *http.Server
	router     *gin.Engine
}

// NewServer 创建新的HTTP服务器
func NewServer(cfg *config.Config, coreLogic *core.Logic) *Server {
	// 设置Gin模式
	if cfg.Log.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	server := &Server{
		config:    cfg,
		coreLogic: coreLogic,
		router:    router,
	}

	// 设置路由
	server.setupRoutes()

	return server
}

// Start 启动HTTP服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf(":%d", s.config.Server.HTTPPort)

	s.httpServer = &http.Server{
		Addr:    addr,
		Handler: s.router,
	}

	slog.Info("HTTP server starting", "addr", addr)

	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("HTTP server failed", "error", err)
		}
	}()

	return nil
}

// Stop 停止HTTP服务器
func (s *Server) Stop() error {
	if s.httpServer == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := s.httpServer.Shutdown(ctx)
	if err != nil {
		slog.Error("Failed to shutdown HTTP server", "error", err)
		return err
	}

	slog.Info("HTTP server stopped")
	return nil
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// Swagger 文档路由 - 仅在 debug 模式下启用
	if s.config.Log.Level == "debug" {
		s.router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		slog.Info("Swagger UI enabled", "url", "http://localhost:"+fmt.Sprintf("%d", s.config.Server.HTTPPort)+"/swagger/index.html")
	}

	// API v1 路由组
	v1 := s.router.Group("/api/v1")
	{
		// 获取设备列表
		v1.GET("/devices", s.getDevices)

		// 视频流相关
		stream := v1.Group("/stream")
		{
			stream.POST("/request", s.requestStream)
		}

		// 控制相关
		control := v1.Group("/control")
		{
			control.POST("/ptz", s.controlPTZ)
		}
	}

	// 健康检查
	s.router.GET("/health", s.healthCheck)
}

// getDevices 获取设备列表
// @Summary 获取设备列表
// @Description 根据平台ID获取该平台下的所有设备信息
// @Tags devices
// @Accept json
// @Produce json
// @Param platform_id query string false "平台ID，不传则获取所有平台的设备"
// @Success 200 {object} models.APIResponse{data=[]models.Device} "成功返回设备列表"
// @Failure 500 {object} models.APIResponse "服务器内部错误"
// @Router /devices [get]
func (s *Server) getDevices(c *gin.Context) {
	platformID := c.Query("platform_id")

	slog.Info("Received device list request", "platform_id", platformID)

	devices, err := s.coreLogic.GetDevices(platformID)
	if err != nil {
		slog.Error("Failed to get devices", "error", err, "platform_id", platformID)

		var code int
		var message string

		if err.Error() == "platform not registered or timeout" {
			code = 5001
			message = err.Error()
		} else {
			code = 5000
			message = "Internal server error"
		}

		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    code,
			Message: message,
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    0,
		Message: "success",
		Data:    devices,
	})
}

// requestStream 请求视频流
// @Summary 请求视频流
// @Description 向指定设备请求视频流，返回SSRC和会话ID
// @Tags stream
// @Accept json
// @Produce json
// @Param request body models.StreamRequestBody true "视频流请求参数"
// @Success 200 {object} models.APIResponse{data=models.StreamResponseData} "成功发起视频流请求"
// @Failure 400 {object} models.APIResponse "请求参数错误"
// @Failure 500 {object} models.APIResponse "服务器内部错误或设备未找到"
// @Router /stream/request [post]
func (s *Server) requestStream(c *gin.Context) {
	var req models.StreamRequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("Invalid request body", "error", err)
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	slog.Info("Received stream request",
		"gb_id", req.GBID,
		"receive_ip", req.ReceiveIP,
		"receive_port", req.ReceivePort)

	responseData, err := s.coreLogic.RequestStream(req.GBID, req.ReceiveIP, req.ReceivePort)
	if err != nil {
		slog.Error("Failed to request stream", "error", err, "gb_id", req.GBID)

		var code int
		var message string

		if err.Error() == "device not found" {
			code = 4004
			message = "device with gb_id not found"
		} else {
			code = 5000
			message = "Internal server error"
		}

		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    code,
			Message: message,
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    0,
		Message: "stream request sent successfully",
		Data:    responseData,
	})
}

// controlPTZ 云台控制
// @Summary 云台控制
// @Description 控制指定设备的云台运动，支持上下左右移动、缩放和停止
// @Tags control
// @Accept json
// @Produce json
// @Param request body models.PTZRequestBody true "云台控制参数"
// @Success 200 {object} models.APIResponse "成功发送云台控制命令"
// @Failure 400 {object} models.APIResponse "请求参数错误或无效的云台命令"
// @Failure 500 {object} models.APIResponse "服务器内部错误或设备未找到"
// @Router /control/ptz [post]
func (s *Server) controlPTZ(c *gin.Context) {
	var req models.PTZRequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("Invalid PTZ request body", "error", err)
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	slog.Info("Received PTZ control request",
		"gb_id", req.GBID,
		"command", req.Command,
		"speed", req.Speed)

	err := s.coreLogic.ControlPTZ(req.GBID, req.Command, req.Speed)
	if err != nil {
		slog.Error("Failed to control PTZ", "error", err, "gb_id", req.GBID)

		var code int
		var message string

		if err.Error() == "device not found" {
			code = 4004
			message = "device with gb_id not found"
		} else if err.Error() == "invalid ptz command" {
			code = 4000
			message = "invalid ptz command"
		} else {
			code = 5000
			message = "Internal server error"
		}

		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    code,
			Message: message,
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    0,
		Message: "ptz command sent successfully",
	})
}

// healthCheck 健康检查
// @Summary 健康检查
// @Description 检查服务器运行状态
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} object{status=string,timestamp=int} "服务器运行正常"
// @Router /health [get]
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
	})
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
