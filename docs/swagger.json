{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "GB28181 视频监控网关 RESTful API 文档", "title": "GB28181 Gateway API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/control/ptz": {"post": {"description": "控制指定设备的云台运动，支持上下左右移动、缩放和停止", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["control"], "summary": "云台控制", "parameters": [{"description": "云台控制参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.PTZRequestBody"}}], "responses": {"200": {"description": "成功发送云台控制命令", "schema": {"$ref": "#/definitions/models.APIResponse"}}, "400": {"description": "请求参数错误或无效的云台命令", "schema": {"$ref": "#/definitions/models.APIResponse"}}, "500": {"description": "服务器内部错误或设备未找到", "schema": {"$ref": "#/definitions/models.APIResponse"}}}}}, "/devices": {"get": {"description": "根据平台ID获取该平台下的所有设备信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["devices"], "summary": "获取设备列表", "parameters": [{"type": "string", "description": "平台ID，不传则获取所有平台的设备", "name": "platform_id", "in": "query"}], "responses": {"200": {"description": "成功返回设备列表", "schema": {"allOf": [{"$ref": "#/definitions/models.APIResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.Device"}}}}]}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/models.APIResponse"}}}}}, "/health": {"get": {"description": "检查服务器运行状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["health"], "summary": "健康检查", "responses": {"200": {"description": "服务器运行正常", "schema": {"type": "object", "properties": {"status": {"type": "string"}, "timestamp": {"type": "integer"}}}}}}}, "/stream/request": {"post": {"description": "向指定设备请求视频流，返回SSRC和会话ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stream"], "summary": "请求视频流", "parameters": [{"description": "视频流请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.StreamRequestBody"}}], "responses": {"200": {"description": "成功发起视频流请求", "schema": {"allOf": [{"$ref": "#/definitions/models.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.StreamResponseData"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/models.APIResponse"}}, "500": {"description": "服务器内部错误或设备未找到", "schema": {"$ref": "#/definitions/models.APIResponse"}}}}}}, "definitions": {"models.APIResponse": {"description": "通用API响应格式", "type": "object", "properties": {"code": {"type": "integer", "example": 0}, "data": {}, "message": {"type": "string", "example": "success"}}}, "models.Device": {"description": "摄像头设备信息", "type": "object", "properties": {"gb_id": {"type": "string", "example": "34020000001320000001"}, "ip": {"type": "string", "example": "*************"}, "name": {"type": "string", "example": "Camera 01"}, "platform_id": {"type": "string", "example": "34020000002000000001"}, "status": {"type": "string", "enum": ["ON", "OFF"], "example": "ON"}}}, "models.PTZRequestBody": {"description": "云台控制请求参数", "type": "object", "required": ["command", "gb_id"], "properties": {"command": {"type": "string", "enum": ["up", "down", "left", "right", "zoom_in", "zoom_out", "stop"], "example": "up"}, "gb_id": {"type": "string", "example": "34020000001320000001"}, "speed": {"type": "integer", "maximum": 255, "minimum": 0, "example": 128}}}, "models.StreamRequestBody": {"description": "视频流请求参数", "type": "object", "required": ["gb_id", "receive_ip", "receive_port"], "properties": {"gb_id": {"type": "string", "example": "34020000001320000001"}, "receive_ip": {"type": "string", "example": "*************"}, "receive_port": {"type": "integer", "example": 8000}}}, "models.StreamResponseData": {"description": "视频流请求响应数据", "type": "object", "properties": {"session_id": {"type": "string", "example": "session_123456"}, "ssrc": {"type": "string", "example": "**********"}}}}, "tags": [{"description": "设备管理相关接口", "name": "devices"}, {"description": "视频流控制相关接口", "name": "stream"}, {"description": "设备控制相关接口", "name": "control"}, {"description": "健康检查相关接口", "name": "health"}]}