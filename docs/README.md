# GB28181 Gateway API 文档

本项目已集成 [Swagger](https://swagger.io/) 用于自动生成 RESTful API 文档。

## 安全说明

**⚠️ 重要**: API 文档仅在 **debug 模式** 下可访问，以确保生产环境的安全性。

- **开发环境**: 设置 `log.level: "debug"` 启用 API 文档
- **生产环境**: 使用 `log.level: "info"` 或更高级别自动禁用 API 文档

## 访问 API 文档

在 debug 模式下启动服务器后，可以通过以下方式访问 API 文档：

### Swagger UI (推荐)
- **URL**: http://localhost:8080/swagger/index.html
- **描述**: 交互式 API 文档界面，可以直接测试 API

### JSON 格式
- **URL**: http://localhost:8080/swagger/doc.json
- **描述**: OpenAPI 3.0 规范的 JSON 格式文档

### YAML 格式
- **URL**: http://localhost:8080/swagger/doc.yaml
- **描述**: OpenAPI 3.0 规范的 YAML 格式文档

## API 概览

### 设备管理
- `GET /api/v1/devices` - 获取设备列表

### 视频流控制
- `POST /api/v1/stream/request` - 请求视频流

### 设备控制
- `POST /api/v1/control/ptz` - 云台控制

### 健康检查
- `GET /health` - 服务器健康检查

## 重新生成文档

当 API 发生变更时，需要重新生成 Swagger 文档：

```bash
# 安装 swag 工具（如果尚未安装）
go install github.com/swaggo/swag/cmd/swag@latest

# 生成文档
swag init -g internal/http/server.go -o docs
```

## 添加新的 API 注解

在添加新的 API 端点时，请按照以下格式添加 Swagger 注解：

```go
// functionName 函数描述
// @Summary 简短描述
// @Description 详细描述
// @Tags 标签名
// @Accept json
// @Produce json
// @Param paramName query/path/body string true/false "参数描述"
// @Success 200 {object} ResponseType "成功响应描述"
// @Failure 400 {object} models.APIResponse "错误响应描述"
// @Router /api/path [method]
func functionName(c *gin.Context) {
    // 函数实现
}
```

## 注解说明

- `@Summary`: API 的简短描述
- `@Description`: API 的详细描述
- `@Tags`: API 分组标签
- `@Accept`: 接受的内容类型
- `@Produce`: 返回的内容类型
- `@Param`: 参数定义
  - 格式: `name location type required "description"`
  - location: query, path, body, header, formData
- `@Success`: 成功响应定义
- `@Failure`: 错误响应定义
- `@Router`: 路由定义

## 模型注解

为数据模型添加 Swagger 注解示例：

```go
// User 用户信息
// @Description 用户信息结构
type User struct {
    ID   int    `json:"id" example:"1" description:"用户ID"`
    Name string `json:"name" example:"张三" description:"用户名称"`
}
```

## 配置说明

Swagger 配置在 `internal/http/server.go` 文件顶部：

```go
// @title GB28181 Gateway API
// @version 1.0
// @description GB28181 视频监控网关 RESTful API 文档
// @host localhost:8080
// @BasePath /api/v1
```

可以根据实际部署环境修改 `@host` 配置。
