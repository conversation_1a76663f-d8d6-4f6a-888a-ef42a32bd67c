basePath: /api/v1
definitions:
  models.APIResponse:
    description: 通用API响应格式
    properties:
      code:
        example: 0
        type: integer
      data: {}
      message:
        example: success
        type: string
    type: object
  models.Device:
    description: 摄像头设备信息
    properties:
      gb_id:
        example: "34020000001320000001"
        type: string
      ip:
        example: *************
        type: string
      name:
        example: Camera 01
        type: string
      platform_id:
        example: "34020000002000000001"
        type: string
      status:
        enum:
        - "ON"
        - "OFF"
        example: "ON"
        type: string
    type: object
  models.PTZRequestBody:
    description: 云台控制请求参数
    properties:
      command:
        enum:
        - up
        - down
        - left
        - right
        - zoom_in
        - zoom_out
        - stop
        example: up
        type: string
      gb_id:
        example: "34020000001320000001"
        type: string
      speed:
        example: 128
        maximum: 255
        minimum: 0
        type: integer
    required:
    - command
    - gb_id
    type: object
  models.StreamRequestBody:
    description: 视频流请求参数
    properties:
      gb_id:
        example: "34020000001320000001"
        type: string
      receive_ip:
        example: *************
        type: string
      receive_port:
        example: 8000
        type: integer
    required:
    - gb_id
    - receive_ip
    - receive_port
    type: object
  models.StreamResponseData:
    description: 视频流请求响应数据
    properties:
      session_id:
        example: session_123456
        type: string
      ssrc:
        example: "**********"
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: GB28181 视频监控网关 RESTful API 文档
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: GB28181 Gateway API
  version: "1.0"
paths:
  /control/ptz:
    post:
      consumes:
      - application/json
      description: 控制指定设备的云台运动，支持上下左右移动、缩放和停止
      parameters:
      - description: 云台控制参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.PTZRequestBody'
      produces:
      - application/json
      responses:
        "200":
          description: 成功发送云台控制命令
          schema:
            $ref: '#/definitions/models.APIResponse'
        "400":
          description: 请求参数错误或无效的云台命令
          schema:
            $ref: '#/definitions/models.APIResponse'
        "500":
          description: 服务器内部错误或设备未找到
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 云台控制
      tags:
      - control
  /devices:
    get:
      consumes:
      - application/json
      description: 根据平台ID获取该平台下的所有设备信息
      parameters:
      - description: 平台ID，不传则获取所有平台的设备
        in: query
        name: platform_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回设备列表
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.Device'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 获取设备列表
      tags:
      - devices
  /health:
    get:
      consumes:
      - application/json
      description: 检查服务器运行状态
      produces:
      - application/json
      responses:
        "200":
          description: 服务器运行正常
          schema:
            properties:
              status:
                type: string
              timestamp:
                type: integer
            type: object
      summary: 健康检查
      tags:
      - health
  /stream/request:
    post:
      consumes:
      - application/json
      description: 向指定设备请求视频流，返回SSRC和会话ID
      parameters:
      - description: 视频流请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.StreamRequestBody'
      produces:
      - application/json
      responses:
        "200":
          description: 成功发起视频流请求
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.StreamResponseData'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.APIResponse'
        "500":
          description: 服务器内部错误或设备未找到
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 请求视频流
      tags:
      - stream
schemes:
- http
- https
swagger: "2.0"
tags:
- description: 设备管理相关接口
  name: devices
- description: 视频流控制相关接口
  name: stream
- description: 设备控制相关接口
  name: control
- description: 健康检查相关接口
  name: health
