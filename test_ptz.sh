#!/bin/bash

# PTZ控制功能测试脚本

BASE_URL="http://localhost:8080"
DEVICE_ID="34020000001320000001"

echo "=== GB-Gateway PTZ控制功能测试 ==="

# 检查服务器状态
echo "1. 检查服务器状态..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ 服务器未运行，请先启动 gb-gateway"
    exit 1
fi
echo "✓ 服务器正在运行"

# 初始化设备数据
echo "2. 初始化设备数据..."
curl -s "$BASE_URL/api/v1/devices" > /dev/null
echo "✓ 设备数据已初始化"

# 测试各种PTZ控制命令
echo "3. 测试PTZ控制命令..."

# 测试向左移动
echo "   3.1 测试向左移动..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"left\",\"speed\":100}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "   ✓ 向左移动命令发送成功"
else
    echo "   ❌ 向左移动命令失败: $RESPONSE"
fi

# 测试向右移动
echo "   3.2 测试向右移动..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"right\",\"speed\":120}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "   ✓ 向右移动命令发送成功"
else
    echo "   ❌ 向右移动命令失败: $RESPONSE"
fi

# 测试向上移动
echo "   3.3 测试向上移动..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"up\",\"speed\":80}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "   ✓ 向上移动命令发送成功"
else
    echo "   ❌ 向上移动命令失败: $RESPONSE"
fi

# 测试向下移动
echo "   3.4 测试向下移动..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"down\",\"speed\":90}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "   ✓ 向下移动命令发送成功"
else
    echo "   ❌ 向下移动命令失败: $RESPONSE"
fi

# 测试放大
echo "   3.5 测试放大..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"zoom_in\",\"speed\":60}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "   ✓ 放大命令发送成功"
else
    echo "   ❌ 放大命令失败: $RESPONSE"
fi

# 测试缩小
echo "   3.6 测试缩小..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"zoom_out\",\"speed\":70}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "   ✓ 缩小命令发送成功"
else
    echo "   ❌ 缩小命令失败: $RESPONSE"
fi

# 测试停止
echo "   3.7 测试停止..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"stop\",\"speed\":0}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "0" ]; then
    echo "   ✓ 停止命令发送成功"
else
    echo "   ❌ 停止命令失败: $RESPONSE"
fi

# 测试错误处理
echo "4. 测试错误处理..."

# 测试无效设备ID
echo "   4.1 测试无效设备ID..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d '{"gb_id":"invalid_device","command":"left","speed":100}')
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "4004" ]; then
    echo "   ✓ 无效设备ID错误处理正确"
else
    echo "   ❌ 无效设备ID错误处理失败: $RESPONSE"
fi

# 测试无效命令
echo "   4.2 测试无效命令..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"invalid_command\",\"speed\":100}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "4000" ]; then
    echo "   ✓ 无效命令错误处理正确"
else
    echo "   ❌ 无效命令错误处理失败: $RESPONSE"
fi

# 测试无效速度
echo "   4.3 测试无效速度..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/control/ptz" \
    -H "Content-Type: application/json" \
    -d "{\"gb_id\":\"$DEVICE_ID\",\"command\":\"left\",\"speed\":300}")
CODE=$(echo "$RESPONSE" | jq -r '.code')
if [ "$CODE" = "4000" ]; then
    echo "   ✓ 无效速度错误处理正确"
else
    echo "   ❌ 无效速度错误处理失败: $RESPONSE"
fi

echo ""
echo "=== PTZ控制功能测试完成 ==="
echo "✓ 所有PTZ控制命令均已测试"
echo "✓ SIP MESSAGE消息已发送到目标设备"
echo "✓ 错误处理功能正常"
