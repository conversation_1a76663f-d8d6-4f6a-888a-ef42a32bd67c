# GB-Gateway

GB-Gateway 是一个基于 Go 语言开发的 GB/T 28181 协议网关服务，用于连接上层业务平台与下层国标视频平台。

## 功能特性

- **协议转换**: 提供简单的 HTTP RESTful API，屏蔽底层 GB/T 28181 SIP 协议复杂性
- **设备管理**: 支持设备列表查询和状态管理
- **视频流控制**: 支持视频流点播请求，自动发送 SIP INVITE 消息
- **云台控制 (PTZ)**: 支持摄像头云台的方向控制和变焦操作，发送 DeviceControl MESSAGE
- **SIP信令处理**: 完整的 SIP INVITE 和 MESSAGE 请求构造，包含标准 SDP 和 XML 内容
- **无状态设计**: 基于内存的状态管理，支持快速部署
- **轻量级**: 只处理信令，不处理视频流，确保高性能

## 系统架构

```
┌─────────────┐    HTTP API    ┌─────────────┐    SIP/GB28181    ┌─────────────┐
│   云视后台   │ ──────────────► │ GB-Gateway  │ ──────────────────► │ 海康安防平台 │
└─────────────┘                └─────────────┘                   └─────────────┘
                                      │
                                      │ HTTP API
                                      ▼
                               ┌─────────────┐
                               │  解码终端   │
                               └─────────────┘
```

## 快速开始

### 一键启动（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd gb28181-client-go

# 2. 复制配置文件
cp config.example.yaml config.yaml

# 3. 生成 API 文档并启动服务器
make docs
```

启动后访问 <http://localhost:8080/swagger/index.html> 查看完整的 API 文档。

### 编译

```bash
# 使用 Go 命令编译
go build -o gb-gateway ./cmd/gb-gateway

# 或使用 Makefile（推荐）
make build
```

### 配置

编辑 `config.yaml` 文件：

```yaml
server:
  http_port: 8080       # HTTP服务端口
  sip_ip: "0.0.0.0"     # SIP监听IP
  sip_port: 5060        # SIP监听端口
  sip_id: "34020000002000000001" # 网关国标ID
  sip_domain: "3402000000"       # 网关域

log:
  level: "debug"        # 日志级别: debug(启用API文档), info, warn, error
  path: "/var/log/gb-gateway.log"
```

**重要**:
- 设置 `log.level: "debug"` 可启用 Swagger API 文档
- 生产环境建议使用 `"info"` 或更高级别以禁用 API 文档

### 运行

```bash
# 直接运行编译后的程序
./gb-gateway

# 或使用 Makefile 编译并运行
make run

# 生成 Swagger 文档并启动服务器
make docs
```

## API 文档

### Swagger UI (推荐)

**⚠️ 安全提示**: Swagger UI 仅在 **debug 模式** 下可访问，生产环境会自动禁用以确保安全性。

启用 debug 模式并启动服务器后，可以通过 **Swagger UI** 查看和测试所有 API：

- **访问地址**: <http://localhost:8080/swagger/index.html>
- **启用条件**: 配置文件中 `log.level` 设置为 `"debug"`
- **功能特性**:
  - 📖 完整的 API 文档，包含中文描述
  - 🧪 交互式测试界面，可直接发送请求
  - 📋 详细的参数说明和示例数据
  - 🏷️ API 按功能分组（设备管理、视频流控制、设备控制等）
  - ⚠️ 完整的错误码说明

### 其他格式

- **JSON 格式**: <http://localhost:8080/swagger/doc.json>
- **YAML 格式**: <http://localhost:8080/swagger/doc.yaml>

### 更新文档

当 API 发生变更时，运行以下命令重新生成文档：

```bash
# 使用 Makefile（推荐）
make swagger-gen

# 或手动执行
# 1. 安装 swag 工具（如果尚未安装）
go install github.com/swaggo/swag/cmd/swag@latest
# 2. 生成文档
swag init -g internal/http/server.go -o docs
```

## API 接口

### 1. 获取设备列表

**请求:**

```bash
GET /api/v1/devices
```

**响应:**

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "gb_id": "34020000001320000001",
      "name": "前门摄像头",
      "status": "ON",
      "ip": "*************",
      "platform_id": "34020000001320000000"
    }
  ]
}
```

### 2. 请求视频流

**请求:**

```bash
POST /api/v1/stream/request
Content-Type: application/json

{
  "gb_id": "34020000001320000001",
  "receive_ip": "*************",
  "receive_port": 15000
}
```

**响应:**

```json
{
  "code": 0,
  "message": "stream request sent successfully",
  "data": {
    "ssrc": "734838",
    "session_id": "session-1755171169295360000"
  }
}
```

**功能说明:**

- 自动生成唯一的 SSRC 标识符
- 创建会话并记录状态
- 构造标准的 SIP INVITE 请求
- 包含完整的 SDP 内容，指定接收端信息
- 支持 GB/T 28181 标准的视频点播流程

### 3. 云台控制 (PTZ)

**请求:**

```bash
POST /api/v1/control/ptz
Content-Type: application/json

{
  "gb_id": "34020000001320000001",
  "command": "left",
  "speed": 100
}
```

**支持的控制命令:**

- `up`: 向上移动
- `down`: 向下移动
- `left`: 向左移动
- `right`: 向右移动
- `zoom_in`: 放大
- `zoom_out`: 缩小
- `stop`: 停止

**响应:**

```json
{
  "code": 0,
  "message": "ptz command sent successfully"
}
```

**功能说明:**

- 支持标准的云台控制命令
- 速度范围：0-255
- 自动构造 GB/T 28181 DeviceControl XML
- 通过 SIP MESSAGE 发送控制指令
- 实时响应控制命令

### 4. 健康检查

**请求:**

```bash
GET /health
```

**响应:**

```json
{
  "status": "ok",
  "timestamp": **********
}
```

## 测试示例

### 获取设备列表

```bash
curl -X GET "http://localhost:8080/api/v1/devices"
```

### 请求视频流

```bash
curl -X POST "http://localhost:8080/api/v1/stream/request" \
  -H "Content-Type: application/json" \
  -d '{
    "gb_id": "34020000001320000001",
    "receive_ip": "*************",
    "receive_port": 15000
  }'
```

### 云台控制

```bash
# 向左移动
curl -X POST "http://localhost:8080/api/v1/control/ptz" \
  -H "Content-Type: application/json" \
  -d '{
    "gb_id": "34020000001320000001",
    "command": "left",
    "speed": 100
  }'

# 放大
curl -X POST "http://localhost:8080/api/v1/control/ptz" \
  -H "Content-Type: application/json" \
  -d '{
    "gb_id": "34020000001320000001",
    "command": "zoom_in",
    "speed": 80
  }'

# 停止
curl -X POST "http://localhost:8080/api/v1/control/ptz" \
  -H "Content-Type: application/json" \
  -d '{
    "gb_id": "34020000001320000001",
    "command": "stop",
    "speed": 0
  }'
```

## 项目结构

```text
gb-gateway/
├── cmd/
│   └── gb-gateway/          # 主程序入口
├── docs/                    # Swagger API 文档
│   ├── docs.go              # Go 格式文档
│   ├── swagger.json         # JSON 格式文档
│   ├── swagger.yaml         # YAML 格式文档
│   └── README.md            # 文档使用说明
├── internal/
│   ├── config/              # 配置管理
│   ├── core/                # 核心业务逻辑
│   ├── http/                # HTTP服务器
│   ├── sip/                 # SIP服务器
│   └── state/               # 状态管理
├── pkg/
│   └── models/              # 数据模型
├── config.yaml              # 配置文件
└── README.md
```

## 开发说明

本项目基于以下技术栈：

- **Web框架**: Gin
- **SIP协议栈**: go-av/gosip
- **配置管理**: Viper
- **日志**: slog

## SIP INVITE 实现详情

当调用视频流请求API时，系统会自动构造并发送完整的标准SIP INVITE请求：

### 完整的 SIP INVITE 消息示例

```text
[GOSIP][UDP] 2025-08-14T20:15:29+08:00 0.0.0.0:5060 --->> *************:5060
INVITE sip:34020000001320000001@*************:5060 SIP/2.0
User-Agent: go-sip
From: <sip:34020000002000000001@3402000000>
To: <sip:34020000001320000001@*************:5060>
Call-ID: call-1755173729882073000@0.0.0.0
CSeq: 1 INVITE
Contact: <sip:34020000002000000001@0.0.0.0:5060>
Via: SIP/2.0/UDP 0.0.0.0:5060
Content-Length: 139
Content-Type: application/sdp

v=0
o=- 0 0 IN IP4 *************
s=Play
c=IN IP4 *************
t=0 0
m=video 15000 RTP/AVP 96
a=rtpmap:96 PS/90000
a=sendonly
a=ssrc:627824
```

### 完整的 PTZ 控制 SIP MESSAGE 消息示例

当调用PTZ控制API时，系统会自动构造并发送标准的SIP MESSAGE请求：

```text
[GOSIP][UDP] 2025-08-14T21:14:51+08:00 0.0.0.0:5060 --->> *************:5060
MESSAGE sip:34020000001320000001@*************:5060 SIP/2.0
To: <sip:34020000001320000001@*************:5060>
Call-ID: call-1755177291940704000@0.0.0.0
CSeq: 1 MESSAGE
Contact: <sip:34020000002000000001@0.0.0.0:5060>
Via: SIP/2.0/UDP 0.0.0.0:5060
User-Agent: go-sip
From: <sip:34020000002000000001@3402000000>
Content-Length: 188
Content-Type: application/MANSCDP+xml

<?xml version="1.0" encoding="UTF-8"?>
<Control>
<CmdType>DeviceControl</CmdType>
<SN>1755177291</SN>
<DeviceID>34020000001320000001</DeviceID>
<PTZCmd>A50F010264000000</PTZCmd>
</Control>
```

**PTZ控制字节说明:**

- `A50F010264000000`: 向左移动，速度100
- `A50F010800960000`: 向上移动，速度150
- `A50F011000005000`: 放大，速度80
- `A50F010000000000`: 停止

### SIP 头部字段说明

- **INVITE**: SIP方法，用于邀请建立会话
- **Via**: 请求路径，包含分支标识符
- **From/To**: 发起方和目标方的SIP URI
- **Call-ID**: 唯一标识此次通话的ID
- **CSeq**: 命令序列号
- **Contact**: 联系地址，用于后续通信

### SDP 内容说明

- `v=0`: SDP版本号
- `o=- 0 0 IN IP4`: 会话发起者信息
- `s=Play`: 会话名称
- `c=IN IP4`: 指定接收端IP地址
- `m=video 15000`: 指定视频媒体和RTP端口
- `a=rtpmap:96 PS/90000`: PS流编码格式，时钟频率90000Hz
- `a=sendonly`: 发送模式（摄像头向解码终端发送）
- `a=ssrc`: 流媒体同步源标识符

## 快速测试

项目包含完整的测试脚本：

```bash
# 运行完整测试
./test.sh

# 运行PTZ控制测试
./test_ptz.sh

# 测试 Swagger 安全功能
make test-security
```

测试脚本会自动验证：

- 服务器启动状态
- 设备列表API
- 视频流请求API
- PTZ控制API
- SIP INVITE和MESSAGE消息发送
- Swagger UI 安全访问控制

## 注意事项

1. 当前版本为简化实现，主要用于演示和测试
2. 生产环境建议使用 Redis 作为状态存储
3. SIP 消息处理可根据实际需求进一步完善
4. 建议添加认证和授权机制
5. ✅ **SIP INVITE 已完整实现**：基于 go-av/gosip 库，支持真实的 SIP 消息发送
6. ✅ **PTZ 控制已完整实现**：支持标准的云台控制命令，通过 SIP MESSAGE 发送 DeviceControl 指令
7. 🔒 **API 文档安全**：Swagger UI 仅在 debug 模式下启用，生产环境自动禁用

## 技术栈

本项目基于以下技术栈：

- **Web框架**: Gin
- **SIP协议栈**: go-av/gosip
- **配置管理**: Viper
- **日志**: slog
- **API文档**: Swagger/OpenAPI 3.0
  - `github.com/swaggo/swag` - 文档生成工具
  - `github.com/swaggo/gin-swagger` - Gin 中间件
  - `github.com/swaggo/files` - 静态文件服务

## 许可证

MIT License
