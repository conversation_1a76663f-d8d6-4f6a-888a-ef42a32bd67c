package models

import "time"

// Platform 代表一个注册的平台
// @Description 注册的平台信息
type Platform struct {
	ID       string    `json:"id" example:"34020000002000000001" description:"平台ID"`
	SIPURI   string    `json:"sip_uri" example:"sip:34020000002000000001@*************:5060" description:"SIP URI"`
	Expires  int       `json:"expires" example:"3600" description:"注册过期时间(秒)"`
	LastSeen time.Time `json:"last_seen" example:"2023-01-01T12:00:00Z" description:"最后活跃时间"`
}

// Device 代表一个摄像头设备
// @Description 摄像头设备信息
type Device struct {
	GBID       string `json:"gb_id" example:"34020000001320000001" description:"设备国标ID"`
	Name       string `json:"name" example:"Camera 01" description:"设备名称"`
	Status     string `json:"status" example:"ON" description:"设备状态" enums:"ON,OFF"`
	IP         string `json:"ip" example:"*************" description:"设备IP地址"`
	PlatformID string `json:"platform_id" example:"34020000002000000001" description:"所属平台ID"`
}

// StreamSession 代表一个点播会话
// @Description 视频流会话信息
type StreamSession struct {
	SessionID   string    `json:"session_id" example:"session_123456" description:"会话ID"`
	GBID        string    `json:"gb_id" example:"34020000001320000001" description:"设备国标ID"`
	SSRC        string    `json:"ssrc" example:"1234567890" description:"SSRC标识"`
	DialogID    string    `json:"dialog_id" example:"dialog_123" description:"SIP对话ID"`
	Destination string    `json:"destination" example:"*************:8000" description:"目标地址"`
	StartTime   time.Time `json:"start_time" example:"2023-01-01T12:00:00Z" description:"开始时间"`
}

// APIResponse 通用API响应结构
// @Description 通用API响应格式
type APIResponse struct {
	Code    int    `json:"code" example:"0" description:"响应码，0表示成功"`
	Message string `json:"message" example:"success" description:"响应消息"`
	Data    any    `json:"data,omitempty" description:"响应数据"`
}

// StreamRequestBody 视频流请求体
// @Description 视频流请求参数
type StreamRequestBody struct {
	GBID        string `json:"gb_id" binding:"required" example:"34020000001320000001" description:"设备国标ID"`
	ReceiveIP   string `json:"receive_ip" binding:"required" example:"*************" description:"接收视频流的IP地址"`
	ReceivePort int    `json:"receive_port" binding:"required" example:"8000" description:"接收视频流的端口"`
}

// StreamResponseData 视频流响应数据
// @Description 视频流请求响应数据
type StreamResponseData struct {
	SSRC      string `json:"ssrc" example:"1234567890" description:"SSRC标识"`
	SessionID string `json:"session_id" example:"session_123456" description:"会话ID"`
}

// PTZRequestBody 云台控制请求体
// @Description 云台控制请求参数
type PTZRequestBody struct {
	GBID    string `json:"gb_id" binding:"required" example:"34020000001320000001" description:"设备国标ID"`
	Command string `json:"command" binding:"required" example:"up" description:"控制命令" enums:"up,down,left,right,zoom_in,zoom_out,stop"`
	Speed   int    `json:"speed" binding:"min=0,max=255" example:"128" description:"控制速度(0-255)"`
}

// PTZCommand 云台控制命令类型
type PTZCommand string

const (
	PTZCommandUp      PTZCommand = "up"
	PTZCommandDown    PTZCommand = "down"
	PTZCommandLeft    PTZCommand = "left"
	PTZCommandRight   PTZCommand = "right"
	PTZCommandZoomIn  PTZCommand = "zoom_in"
	PTZCommandZoomOut PTZCommand = "zoom_out"
	PTZCommandStop    PTZCommand = "stop"
)
