#!/bin/bash

# GB28181 Gateway Swagger Security Test Script
# 测试 Swagger UI 在不同日志级别下的访问控制

set -e

echo "🔒 Testing Swagger UI Security Features"
echo "======================================="

# 函数：等待服务器启动
wait_for_server() {
    echo "⏳ Waiting for server to start..."
    for i in {1..10}; do
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            echo "✅ Server is ready"
            return 0
        fi
        sleep 1
    done
    echo "❌ Server failed to start"
    return 1
}

# 函数：测试 Swagger 访问
test_swagger_access() {
    local mode=$1
    local expected_code=$2
    
    echo "🧪 Testing Swagger UI access in $mode mode..."
    
    local actual_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/swagger/index.html)
    
    if [ "$actual_code" = "$expected_code" ]; then
        echo "✅ $mode mode: Swagger UI returns $actual_code (expected)"
    else
        echo "❌ $mode mode: Swagger UI returns $actual_code (expected $expected_code)"
        return 1
    fi
}

# 函数：更新配置文件日志级别
update_log_level() {
    local level=$1
    sed -i.bak "s/level: \".*\"/level: \"$level\"/" config.yaml
    echo "📝 Updated log level to: $level"
}

# 函数：启动服务器
start_server() {
    echo "🚀 Starting server..."
    make run > /dev/null 2>&1 &
    SERVER_PID=$!
    wait_for_server
}

# 函数：停止服务器
stop_server() {
    if [ ! -z "$SERVER_PID" ]; then
        echo "🛑 Stopping server..."
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        sleep 2
    fi
}

# 备份原始配置
cp config.yaml config.yaml.test_backup

# 清理函数
cleanup() {
    echo "🧹 Cleaning up..."
    stop_server
    mv config.yaml.test_backup config.yaml
    echo "✅ Cleanup completed"
}

# 设置清理陷阱
trap cleanup EXIT

echo ""
echo "📋 Test Plan:"
echo "1. Test with log level 'info' - Swagger should be disabled (404)"
echo "2. Test with log level 'debug' - Swagger should be enabled (200)"
echo ""

# 测试 1: info 模式 - Swagger 应该被禁用
echo "🔍 Test 1: INFO mode (Swagger disabled)"
echo "----------------------------------------"
update_log_level "info"
start_server
test_swagger_access "INFO" "404"
stop_server

echo ""

# 测试 2: debug 模式 - Swagger 应该启用
echo "🔍 Test 2: DEBUG mode (Swagger enabled)"
echo "---------------------------------------"
update_log_level "debug"
start_server
test_swagger_access "DEBUG" "200"

# 额外测试：验证 API 文档 JSON 也可访问
echo "🧪 Testing Swagger JSON API access..."
json_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/swagger/doc.json)
if [ "$json_code" = "200" ]; then
    echo "✅ DEBUG mode: Swagger JSON API returns 200 (expected)"
else
    echo "❌ DEBUG mode: Swagger JSON API returns $json_code (expected 200)"
fi

stop_server

echo ""
echo "🎉 All tests completed successfully!"
echo "✅ Swagger UI security is working correctly:"
echo "   - INFO mode: API documentation disabled (404)"
echo "   - DEBUG mode: API documentation enabled (200)"
echo ""
echo "🔒 Security Summary:"
echo "   - Production environments should use 'info' or higher log levels"
echo "   - Development environments can use 'debug' to access API docs"
echo "   - Swagger UI URL: http://localhost:8080/swagger/index.html"
