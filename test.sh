#!/bin/bash

# GB-Gateway 集成测试脚本

echo "=== GB-Gateway 集成测试 ==="

# 检查服务器是否运行
echo "1. 检查服务器状态..."
curl -s http://localhost:8080/health > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ 服务器正在运行"
else
    echo "✗ 服务器未运行，请先启动 ./gb-gateway"
    exit 1
fi

# 测试健康检查
echo ""
echo "2. 测试健康检查..."
response=$(curl -s http://localhost:8080/health)
echo "响应: $response"
if [[ $response == *"ok"* ]]; then
    echo "✓ 健康检查通过"
else
    echo "✗ 健康检查失败"
fi

# 测试获取设备列表
echo ""
echo "3. 测试获取设备列表..."
response=$(curl -s http://localhost:8080/api/v1/devices)
echo "响应: $response"
if [[ $response == *"success"* ]]; then
    echo "✓ 设备列表获取成功"
else
    echo "✗ 设备列表获取失败"
fi

# 测试视频流请求
echo ""
echo "4. 测试视频流请求..."

# 首先获取设备列表以确保有可用设备
echo "   4.1 获取设备列表..."
curl -s http://localhost:8080/api/v1/devices > /dev/null

# 然后发送视频流请求
echo "   4.2 发送视频流请求..."
response=$(curl -s -X POST "http://localhost:8080/api/v1/stream/request" \
  -H "Content-Type: application/json" \
  -d '{
    "gb_id": "34020000001320000001",
    "receive_ip": "*************",
    "receive_port": 15000
  }')
echo "响应: $response"
if [[ $response == *"stream request sent successfully"* ]]; then
    echo "✓ 视频流请求成功，SIP INVITE已发送"

    # 提取SSRC和会话ID
    ssrc=$(echo $response | grep -o '"ssrc":"[^"]*"' | cut -d'"' -f4)
    session_id=$(echo $response | grep -o '"session_id":"[^"]*"' | cut -d'"' -f4)
    echo "   SSRC: $ssrc"
    echo "   会话ID: $session_id"
else
    echo "✗ 视频流请求失败"
fi

# 测试错误情况
echo ""
echo "5. 测试错误处理..."
response=$(curl -s -X POST "http://localhost:8080/api/v1/stream/request" \
  -H "Content-Type: application/json" \
  -d '{
    "gb_id": "invalid_device_id",
    "receive_ip": "*************",
    "receive_port": 15000
  }')
echo "响应: $response"
if [[ $response == *"device with gb_id not found"* ]]; then
    echo "✓ 错误处理正确"
else
    echo "✗ 错误处理异常"
fi

echo ""
echo "=== 测试完成 ==="
