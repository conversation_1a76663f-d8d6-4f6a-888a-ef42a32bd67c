# 系统架构程

---

## 一、 各组件角色说明

在看图之前，我们先明确一下各个组件在系统中的角色和职责：

* **云视 (后台程序)**: 系统的“大脑”，业务逻辑的核心。它不直接处理信令流或媒体流，而是通过HTTP接口向其他组件下发指令，协调整个系统的工作。
* **海康安防平台**: 传统的视频监控管理平台，负责直接管理和配置IPC摄像头。在此架构中，它扮演一个符合GB28181标准的下级域角色。
* **IPC摄像头**: 视频源设备。
* **GB网关**: 协议转换和信令处理的“翻译官”和“交通枢纽”。
  *  对上（对云视）: 提供简单的HTTP接口，屏蔽复杂的GB28181/SIP细节。
  *  对下（对海康平台）: 使用标准的GB28181/SIP协议进行通信，如设备目录查询、点播请求等。
* **凯撒主机 (KVM主机)**: 大屏拼接和布局的“导演”。它负责管理所有解码终端，并根据指令控制哪个视频画面在哪个解码终端上显示，以及该解码终端在大屏上的具体位置（开窗布局）。
* **解码终端**: 负责解码视频流并在大屏上具体呈现画面的“执行者”。它从IPC接收视频流，并根据凯撒主机的指令在VP6视频墙上开窗显示。
* **VP6视频墙**: 最终的显示设备。

---

## 二、 系统架构图

此图展示了各个组件之间的静态连接关系和主要的通信协议。

```mermaid
graph TD
    subgraph "用户交互层"
        FrontEnd[前端应用]
    end

    subgraph "业务控制层"
        Yunshi[1. 云视后台]
        DB[(asset数据库)]
    end

    subgraph "大屏控制系统"
        Caesar[4. 凯撒主机]
        Decoder[5. 解码终端]
        VP6[6. VP6视频墙]
    end

    subgraph "视频信令与媒体层"
        GBGW[3. GB网关]
        Hikvision[2. 海康安防平台]
        IPC[3. IPC摄像头]
    end

    %% 定义连接关系
    FrontEnd -- "1. HTTP API (开窗请求等)" --> Yunshi
    Yunshi -- "2. HTTP API (获取列表, 开窗)" --> GBGW
    Yunshi -- "3. HTTP API (控制开窗布局)" --> Caesar
    Yunshi -- "4. 读写设备信息" --> DB

    GBGW -- "5. SIP/GB28181 (注册, Catalog, Invite)" --> Hikvision
    
    Hikvision -- "6. 管理/配置" --> IPC
    Hikvision -- "7. SIP/GB28181 (指令)" --> IPC

    Caesar -- "8. HTTP (指令解码)" --> Decoder
    Caesar -- "9. 私有协议 (控制上墙位置)" --> Decoder
    
    Decoder -- "10. HTTP (请求拉流)" --> GBGW
    Decoder -- "11. 视频输出" --> VP6
    
    IPC -- "12. RTP/UDP 视频流" --> Decoder

    %% 样式
    classDef main fill:#e6f3ff,stroke:#333,stroke-width:2px;
    classDef platform fill:#f9f,stroke:#333,stroke-width:2px;
    classDef device fill:#fcf,stroke:#333,stroke-width:2px;

    class Yunshi,GBGW,Caesar,Hikvision main;
    class Decoder,IPC device;
```

**架构图说明:**

1. **前端应用** 通过HTTP API与 **云视后台** 交互，发起如“开窗”等操作。
2. **云视后台** 是总指挥，它通过HTTP API分别向 **GB网关** （用于视频相关操作）和 **凯撒主机** （用于大屏布局操作）下发指令。
3. **GB网关** 作为SIP信令网关，使用GB28181协议与 **海康安防平台** 通信。海康平台已注册到GB网关上。
4. **凯撒主机** 通过HTTP和私有协议控制 **解码终端** 的行为。
5. **核心媒体路径**: 当所有信令协商完成后（红色虚线代表的RTP流），**IPC摄像头** 会将视频流（RTP）直接推送到 **解码终端** 的IP和端口上。解码终端解码后，将画面输出到 **VP6视频墙**。
6. 整个架构实现了 **控制信令** (Control Plane) 和 **媒体数据** (Data Plane) 的分离，云视、凯撒主机等只负责控制，不处理庞大的视频流数据，保证了系统的可扩展性和性能。

---

## 三、 流程一：云视获取所有摄像头列表

此流程图展示了云视如何通过GB网关从海康平台同步设备列表。这里我们使用泳道图来清晰地表示每个组件的职责。

```mermaid
sequenceDiagram
    participant Yunshi as 云视后台
    participant GBGW as GB网关
    participant Hikvision as 海康平台
    participant DB as 数据库

    Yunshi->>+GBGW: 1. 发起HTTP请求 (GET /api/devices)
    Note over GBGW: 收到获取列表请求

    GBGW->>+Hikvision: 2. 发送SIP命令 (Catalog查询)
    Note over Hikvision: 收到Catalog命令，查询下级设备

    Hikvision-->>-GBGW: 3. SIP响应 (返回IPC列表信息)
    Note over GBGW: 收到设备列表，解析数据

    GBGW-->>-Yunshi: 4. HTTP响应 (返回JSON格式的IPC列表)
    Note over Yunshi: 收到列表，准备入库

    Yunshi->>+DB: 5. 将结果存入asset表 (云视id, gb_id)
    DB-->>-Yunshi: 存储成功
```

**流程说明:**

1. **云视** 向 **GB网关** 暴露的HTTP接口发起一个简单的获取设备列表的请求。
2. **GB网关** 将这个HTTP请求翻译成一条GB28181标准的 `Catalog` SIP消息，发送给已注册的 **海康平台**。
3. **海康平台** 收到 `Catalog` 请求后，查询其管理的所有IPC摄像头信息，并通过SIP响应返回给 **GB网关**。
4. **GB网关** 解析SIP响应，将设备信息整理成对云视友好的JSON格式，通过HTTP响应返回给 **云视**。
5. **云视** 收到设备列表后，将`gb_id`（国标ID）和系统内部的ID对应起来，存入 **数据库** 的 `asset` 表中，完成设备同步。

---

## 四、 流程二：开窗

此流程图展示了从前端发起请求，到最终在VP6大屏上看到画面的完整链路。这是一个多方协作的复杂流程。

```mermaid
sequenceDiagram
    participant FrontEnd as 前端
    participant Yunshi as 云视后台
    participant Caesar as 凯撒主机
    participant Decoder as 解码终端 (dc_1)
    participant GBGW as GB网关
    participant Hikvision as 海康平台
    participant IPC as IPC摄像头 (ipc_1)

    FrontEnd->>+Yunshi: 1. 发起开窗请求 (ipc_1, vp6左上角)
    Note over Yunshi: 收到请求, 查询ipc_1的gb_id

    Yunshi->>+Caesar: 2. HTTP请求 (开窗gb_id到左上角)
    Note over Caesar: 收到开窗请求, 分配解码终端dc_1

    Caesar->>+Decoder: 3. HTTP请求 (指令dc_1解码gb_id)
    Caesar->>+Decoder: 4. 私有协议 (指令dc_1上墙位置:左上角)

    Note over Decoder: 收到双重指令, 准备请求视频流
    Decoder->>+GBGW: 5. HTTP请求 (请求gb_id视频流, 携带自身IP/Port)

    Note over GBGW: 收到拉流请求, 准备发起SIP点播
    GBGW->>+Hikvision: 6. 发送SIP Invite (点播gb_id, s=play, t=解码终端IP:Port)

    Note over Hikvision: 收到Invite, 指令IPC推流
    Hikvision->>IPC: (内部指令) 让ipc_1向解码终端IP:Port推流

    Note over IPC: 开始推流
    IPC-->>Decoder: RTP/UDP 视频流

    Note over Decoder: 接收并解码视频流, 输出到大屏指定位置
```

**流程说明:**

1. **前端** 发起开窗请求，指定了要看的摄像头和显示位置。
2. **云视** 作为总调度，兵分两路：
    * 向 **凯撒主机** 发送指令，告诉它哪个国标ID (`gb_id`) 的视频，需要显示在哪个位置。
3. **凯撒主机** 作为大屏“导演”，继续下发指令：
    * 首先选择一个空闲的 **解码终端** (`dc_1`)。
    * 通过HTTP告诉 `dc_1` 它需要解码哪个 `gb_id` 的视频。
    * 通过私有协议告诉 `dc_1` 解码后的画面要输出到大屏的哪个物理位置（左上角）。
4. **解码终端** (`dc_1`) 在收到两个指令后，知道了自己的任务（解码哪个视频，显示在哪里），但它还没有视频流。
5. 于是，`dc_1` 主动向 **GB网关** 发起HTTP请求，说：“我要 `gb_id` 的视频流，请把它发到我的IP和端口上来”。
6. **GB网关** 将这个HTTP请求翻译成GB28181标准的 `SIP Invite` 消息，发给 **海康平台**。这个Invite消息中包含了 `dc_1` 的接收IP和端口。
7. **海康平台** 收到 `Invite` 后，找到对应的 **IPC摄像头** (`ipc_1`)，命令它向 `Invite` 消息中指定的IP和端口（也就是 `dc_1` 的地址）推送RTP视频流。
8. **IPC** 开始向 **解码终端** 推送视频流，解码终端解码后，根据凯撒主机之前的指令，在 **VP6视频墙** 的左上角开窗显示画面。流程结束。

---

## 五、 流程三：PTZ控制流程图

这是一个清晰的泳道图，展示了PTZ控制命令是如何一步步从前端传递到摄像头的。

```mermaid
sequenceDiagram
    participant FrontEnd as 前端
    participant Yunshi as 云视后台
    participant GBGW as GB网关
    participant Hikvision as 海康平台
    participant IPC as IPC摄像头 (ipc_1)

    FrontEnd->>+Yunshi: 1. 发起PTZ控制请求
    Note over FrontEnd, Yunshi: Payload: { "gb_id": "...", "command": "TILT_UP", "speed": 5 }

    Yunshi->>+GBGW: 2. HTTP请求 (POST /api/v1/control/ptz)
    Note over Yunshi, GBGW: Body: { "gb_id": "...", "command": "TILT_UP", "speed": 5 }

    Note over GBGW: 收到PTZ请求, 准备构造SIP MESSAGE
    GBGW->>+Hikvision: 3. 发送SIP MESSAGE (DeviceControl)
    Note over GBGW, Hikvision: XML Body: <Control><CmdType>DeviceControl</CmdType><SN>...</SN><DeviceID>...</DeviceID><PTZCmd>A50F0108050501FF</PTZCmd></Control>

    Note over Hikvision: 收到SIP命令, 解析PTZCmd, 准备控制IPC
    Hikvision->>+IPC: 4. (内部协议) 控制云台向上转动

    IPC-->>-Hikvision: (内部协议) 状态/结果
    Hikvision-->>-GBGW: SIP 200 OK
    GBGW-->>-Yunshi: HTTP 200 OK
    Yunshi-->>-FrontEnd: HTTP 200 OK (控制指令已发送)
```

**流程说明:**

1. **前端** 将要控制的摄像头 `gb_id`、具体的控制指令（如：向上 `TILT_UP`）和速度等参数，通过HTTP请求发送给 **云视后台**。
2. **云视后台** 将这个请求原封不动地（或稍作转换）通过HTTP转发给 **GB网关**。
3. **GB网关** 的核心工作来了：
    * 它接收到HTTP请求。
    * 将`TILT_UP`、`speed`等业务指令，编码成GB/T 28181协议规定的PTZ控制码（一串十六进制字符串）。
    * 将此控制码封装在一个XML结构的 `DeviceControl` 命令中。
    * 将这个XML作为消息体，通过SIP `MESSAGE` 方法发送给 **海康平台**。
4. **海康平台** 收到SIP `MESSAGE`，解析出XML中的PTZ控制码，然后通过它自己的私有协议或标准协议（如ONVIF）去命令 **IPC摄像头** 执行相应的云台转动操作。
5. 指令执行成功后，各层会逐级返回`200 OK`响应，表示指令已成功下发。
